'use server';

/**
 * @fileOverview AI-powered tool to enhance prompt quality for Google Veo standards.
 *
 * - enhancePromptQuality - Enhances the given prompt to align with Google Veo standards.
 * - EnhancePromptQualityInput - Input type for the enhancePromptQuality function.
 * - EnhancePromptQualityOutput - Return type for the enhancePromptQuality function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const EnhancePromptQualityInputSchema = z.object({
  prompt: z.string().describe('The prompt to be enhanced.'),
});
export type EnhancePromptQualityInput = z.infer<typeof EnhancePromptQualityInputSchema>;

const EnhancePromptQualityOutputSchema = z.object({
  enhancedPrompt: z.string().describe('The enhanced prompt aligned with Google Veo standards.'),
  explanation: z.string().describe('Explanation of the enhancements made.'),
});
export type EnhancePromptQualityOutput = z.infer<typeof EnhancePromptQualityOutputSchema>;

export async function enhancePromptQuality(input: EnhancePromptQualityInput): Promise<EnhancePromptQualityOutput> {
  return enhancePromptQualityFlow(input);
}

const enhancePromptQualityPrompt = ai.definePrompt({
  name: 'enhancePromptQualityPrompt',
  input: {schema: EnhancePromptQualityInputSchema},
  output: {schema: EnhancePromptQualityOutputSchema},  prompt: `You are a VEO prompt enhancement tool designed to improve video production prompts to meet professional standards. 
  
  Analyze the following video prompt and enhance it by:
  1. Adding more specific and vivid visual details
  2. Ensuring professional cinematographic descriptions
  3. Using industry-standard terminology
  4. Maintaining a clear and organized structure
  5. Optimizing for video production requirements
  
  Original Prompt:
  {{{prompt}}}
  
  Please provide:
  1. An enhanced version of the prompt with improved details and professional language
  2. A brief explanation of the key improvements made and how they benefit the video production
  `,
});

const enhancePromptQualityFlow = ai.defineFlow(
  {
    name: 'enhancePromptQualityFlow',
    inputSchema: EnhancePromptQualityInputSchema,
    outputSchema: EnhancePromptQualityOutputSchema,
  },
  async input => {
    const {output} = await enhancePromptQualityPrompt(input);
    return output!;
  }
);
