// This is an autogenerated file from Firebase Studio.

'use server';

/**
 * @fileOverview Translates freeform Indonesian text to English.
 *
 * - translateFreeformIndonesian - A function that translates the freeform Indonesian text.
 * - TranslateFreeformIndonesianInput - The input type for the function.
 * - TranslateFreeformIndonesianOutput - The return type for the function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const TranslateFreeformIndonesianInputSchema = z.object({
  text: z.string().describe('The Indonesian text to be translated.'),
});
export type TranslateFreeformIndonesianInput = z.infer<typeof TranslateFreeformIndonesianInputSchema>;

const TranslateFreeformIndonesianOutputSchema = z.object({
  translatedText: z.string().describe('The translated English text.'),
});
export type TranslateFreeformIndonesianOutput = z.infer<typeof TranslateFreeformIndonesianOutputSchema>;

export async function translateFreeformIndonesian(
  input: TranslateFreeformIndonesianInput
): Promise<TranslateFreeformIndonesianOutput> {
  return translateFreeformIndonesianFlow(input);
}

const prompt = ai.definePrompt({
  name: 'translateFreeformIndonesianPrompt',
  input: {schema: TranslateFreeformIndonesianInputSchema},
  output: {schema: TranslateFreeformIndonesianOutputSchema},
  prompt: `Translate the following Indonesian text to English:

{{{text}}}

Provide only the English translation.`,
});

const translateFreeformIndonesianFlow = ai.defineFlow(
  {
    name: 'translateFreeformIndonesianFlow',
    inputSchema: TranslateFreeformIndonesianInputSchema,
    outputSchema: TranslateFreeformIndonesianOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
