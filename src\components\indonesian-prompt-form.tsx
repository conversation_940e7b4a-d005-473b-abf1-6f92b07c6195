"use client";

import { useEffect, useCallback } from "react";
import type { IndonesianPromptFormData, SubjectData } from "@/lib/schemas";
import { IndonesianPromptSchema, defaultIndonesianPromptValues } from "@/lib/schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Plus, Trash2 } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface FormFieldConfig {
  name: string;
  label: string;
  placeholder?: string;
  isTextarea?: boolean;
  selectOptions?: Array<{ value: string; label: string }>;
}

// Define camera movement options
const cameraMovementOptions = [
  { value: "Static", label: "Static" },
  { value: "Pan", label: "Pan" },
  { value: "Tilt", label: "Tilt" },
  { value: "Zoom", label: "Zoom" },
  { value: "Dolly", label: "Dolly" },
  { value: "Tracking", label: "Tracking" },
  { value: "Aerial", label: "Aerial" },
  { value: "Handheld", label: "Handheld" },
  { value: "Lainnya", label: "Lainnya..." },
];

// Define lighting options
const lightingOptions = [
  { value: "Natural", label: "Natural" },
  { value: "Soft", label: "Soft" },
  { value: "Hard", label: "Hard" },
  { value: "High-key", label: "High-key" },
  { value: "Low-key", label: "Low-key" },
  { value: "Dramatic", label: "Dramatic" },
  { value: "Silhouette", label: "Silhouette" },
  { value: "Lainnya", label: "Lainnya..." },
];

// Define video style options
const videoStyleOptions = [
  { value: "Cinematic", label: "Cinematic" },
  { value: "Documentary", label: "Documentary" },
  { value: "Commercial", label: "Commercial" },
  { value: "Music Video", label: "Music Video" },
  { value: "Vlog", label: "Vlog" },
  { value: "Animation", label: "Animation" },
  { value: "Lainnya", label: "Lainnya..." },
];

// Define video mood options
const videoMoodOptions = [
  { value: "Happy", label: "Happy" },
  { value: "Sad", label: "Sad" },
  { value: "Dramatic", label: "Dramatic" },
  { value: "Mysterious", label: "Mysterious" },
  { value: "Romantic", label: "Romantic" },
  { value: "Energetic", label: "Energetic" },
  { value: "Calm", label: "Calm" },
  { value: "Lainnya", label: "Lainnya..." },
];

// Define sound/music options
const soundMusicOptions = [
  { value: "Upbeat", label: "Upbeat" },
  { value: "Dramatic", label: "Dramatic" },
  { value: "Ambient", label: "Ambient" },
  { value: "Classical", label: "Classical" },
  { value: "Electronic", label: "Electronic" },
  { value: "No Music", label: "No Music" },
  { value: "Lainnya", label: "Lainnya..." },
];

// Define dialog type options
const dialogTypeOptions = [
  { value: "no_dialog", label: "Tanpa dialog" },
  { value: "informative", label: "Informatif" },
  { value: "natural", label: "Dialog natural" },
  { value: "monologue", label: "Monolog" },
  { value: "interview", label: "Interview" },
];

// Define subject field configs
const subjectFieldConfigs: Array<FormFieldConfig> = [
  { name: "name", label: "Nama Karakter", placeholder: "Contoh: Alice" },
  { name: "characteristics", label: "Karakteristik Subjek", placeholder: "Contoh: Pria paruh baya, rambut pirang, tinggi", isTextarea: true },
  { name: "mainAction", label: "Aksi Utama", placeholder: "Contoh: Berlari di taman" },
  { name: "emotion", label: "Emosi", placeholder: "Contoh: Senang, cemas, terkejut" },
];

// Define scene field configs
const sceneFieldConfigs: Array<FormFieldConfig> = [
  { name: "place", label: "Place", placeholder: "e.g., taman kota yang ramai" },
  { name: "time", label: "Time", placeholder: "e.g., sore hari keemasan" },
  { name: "dialogType", label: "Jenis Dialog", selectOptions: dialogTypeOptions, placeholder: "Pilih jenis dialog..." },
  { name: "cameraMovement", label: "Camera Movement", selectOptions: cameraMovementOptions, placeholder: "Detail gerakan kamera..." },
  { name: "lighting", label: "Lighting", selectOptions: lightingOptions, placeholder: "Detail pencahayaan..." },
  { name: "videoStyle", label: "Video Style", selectOptions: videoStyleOptions, placeholder: "Detail gaya video..." },
  { name: "videoMood", label: "Video Mood", selectOptions: videoMoodOptions, placeholder: "Detail suasana video..." },
  { name: "soundMusic", label: "Sound/Music", selectOptions: soundMusicOptions, placeholder: "e.g., musik instrumental yang menenangkan" },
  { name: "spokenSentence", label: "Spoken Sentence", placeholder: "e.g., 'Ini hari yang indah.'", isTextarea: true },
  { name: "additionalDetails", label: "Additional Details", placeholder: "e.g., Mengenakan gaun merah, ada anjing kecil berlarian.", isTextarea: true },
];

interface IndonesianPromptFormProps {
  onFormValuesChange: (data: IndonesianPromptFormData) => void;
  onGeneratePrompts: () => void;
  isProcessing: boolean;
}

export function IndonesianPromptForm({ onFormValuesChange, onGeneratePrompts, isProcessing }: IndonesianPromptFormProps) {
  const form = useForm<IndonesianPromptFormData>({
    resolver: zodResolver(IndonesianPromptSchema),
    defaultValues: defaultIndonesianPromptValues,
    mode: "onChange",
  });

  // Use field array to handle multiple subjects
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "subjects",
  });

  // Menggunakan useEffect dengan debounce untuk mengurangi frekuensi pembaruan
useEffect(() => {
  let timeoutId: NodeJS.Timeout;
   const subscription = form.watch((value, { name, type }) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
       onFormValuesChange(form.getValues());
     }, 300);
   });
   
  return () => {
    clearTimeout(timeoutId);
    subscription.unsubscribe();
  };
 }, [form, onFormValuesChange]);

  const addNewSubject = () => {
    append({ name: "", characteristics: "", mainAction: "", emotion: "" });
  };

  const renderSelectField = (fieldConfig: FormFieldConfig, path: string) => {
    if (!fieldConfig.selectOptions) return null;
    
    return (
      <FormField
        key={path}
        control={form.control}
        name={path as any}
        render={({ field: formFieldProps }) => {
          const currentFieldValue = typeof formFieldProps.value === 'string' ? formFieldProps.value : "";
          const isStandardOption = fieldConfig.selectOptions?.some(
            opt => opt.value === currentFieldValue && opt.value !== 'Lainnya'
          );
          const selectTriggerValue = isStandardOption ? currentFieldValue : "Lainnya";
          const showCustomInput = !isStandardOption || currentFieldValue === "Lainnya";

          return (
            <FormItem className="w-full">
              <FormLabel className="font-medium">
                {fieldConfig.label}
                {path.includes("mainAction") ? ' *' : ''}
              </FormLabel>
              <Select
                value={selectTriggerValue}
                onValueChange={(selectedValue) => {
                  if (selectedValue === "Lainnya") {
                    form.setValue(path as any, "", { shouldValidate: true, shouldDirty: true });
                  } else {
                    form.setValue(path as any, selectedValue, { shouldValidate: true, shouldDirty: true });
                  }
                }}
              >
                <FormControl>
                  <SelectTrigger className="w-full bg-background">
                    <SelectValue placeholder={`Pilih ${fieldConfig.label.toLowerCase()}`} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {fieldConfig.selectOptions?.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {showCustomInput && (
                <Input
                  {...formFieldProps}
                  value={currentFieldValue === "Lainnya" ? "" : currentFieldValue}
                  onChange={(e) => form.setValue(path as any, e.target.value, { shouldValidate: true, shouldDirty: true })}
                  placeholder={fieldConfig.placeholder || `Detail untuk ${fieldConfig.label.toLowerCase()}`}
                  className="mt-2"
                />
              )}
              <FormMessage />
            </FormItem>
          );
        }}
      />
    );
  };

  const renderInputField = (fieldConfig: FormFieldConfig, path: string, index?: number) => {
    if (fieldConfig.selectOptions) {
      return renderSelectField(fieldConfig, path);
    }

    return (
      <FormField
        key={path}
        control={form.control}
        name={path as any}
        render={({ field: formFieldProps }) => (
          <FormItem>
            <FormLabel className="font-medium">
              {fieldConfig.label}
              {(path.includes("name") || path.includes("mainAction")) ? ' *' : ''}
            </FormLabel>
            <FormControl>
              {fieldConfig.isTextarea ? (
                <Textarea 
                  placeholder={fieldConfig.placeholder} 
                  {...formFieldProps} 
                  className={`resize-none bg-background ${fieldConfig.name === "characteristics" ? "h-20" : "h-24"}`}
                />
              ) : (
                <Input 
                  placeholder={fieldConfig.placeholder} 
                  {...formFieldProps} 
                  className="bg-background" 
                />
              )}
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="border-b bg-muted/50">
        <CardTitle className="font-headline text-2xl text-primary">Form Prompt Indonesia</CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <Form {...form}>
          <form className="space-y-8">
            {/* Subject section */}
            <div className="space-y-6">
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-semibold">Detail Subjek</h3>
              </div>
              
              {fields.map((field, index) => (
                <Card key={field.id} className="relative border border-muted-foreground/20 bg-muted/5">
                  <CardContent className="pt-4">
                    <div className="flex justify-between items-center mb-6">
                      <h4 className="text-md font-medium">Karakter {index + 1}</h4>
                      {fields.length > 1 && (
                        <Button 
                          type="button" 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => remove(index)}
                          className="text-destructive hover:text-destructive hover:bg-destructive/10"
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Hapus
                        </Button>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {subjectFieldConfigs.map((fieldConfig) => 
                        renderInputField(
                          fieldConfig, 
                          `subjects.${index}.${fieldConfig.name}`, 
                          index
                        )
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              <Button
                type="button"
                variant="outline"
                className="w-full hover:bg-muted/50"
                onClick={addNewSubject}
              >
                <Plus className="h-4 w-4 mr-2" />
                Tambah Karakter Baru
              </Button>
            </div>

            {/* Audio Character section */}

            {/* Audio Character section */}

            {/* Audio Character section */}

            {/* Audio Character section */}

            {/* Audio Character section */}

            {/* Audio Character section */}

            {/* Audio Character section */}
            <div className="space-y-6">
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-semibold">Audio Karakter</h3>
              </div>
              <Card className="relative border border-muted-foreground/20 bg-muted/5">
                <CardContent className="pt-4">
                  <div className="grid grid-cols-1 gap-6">
                    {renderInputField({ 
                      name: "dialogType", 
                      label: "Jenis Dialog", 
                      selectOptions: dialogTypeOptions, 
                      placeholder: "Pilih jenis dialog..."
                    }, "dialogType")}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Scene settings section */}
            <div className="space-y-6">
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-semibold">Scene Settings</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {sceneFieldConfigs.map((fieldConfig) => 
                  renderInputField(fieldConfig, fieldConfig.name)
                )}
              </div>
            </div>
            
            <div className="flex justify-end pt-4">
              <Button 
                type="button" 
                disabled={isProcessing}
                onClick={onGeneratePrompts}
                className="min-w-[200px]"
              >
                {isProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Buat Prompt (ID & EN)
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

