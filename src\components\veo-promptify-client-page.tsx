"use client";

import { useState, useCallback, useEffect, useTransition } from "react";
import { useToast } from "@/hooks/use-toast";
import { IndonesianPromptFormData, defaultIndonesianPromptValues } from "@/lib/schemas";
import { enhancePromptQuality, EnhancePromptQualityOutput } from "@/ai/flows/enhance-prompt-quality";
import { translateIndonesianPrompt, TranslateIndonesianPromptOutput } from "@/ai/flows/translate-indonesian-prompt";
import { translateFreeformIndonesian } from "@/ai/flows/translate-freeform-indonesian-flow";
import { debounce } from "lodash";
import { AppHeader } from "@/components/app-header";
import { IndonesianPromptForm } from "@/components/indonesian-prompt-form";
import { GeneratedPromptsDisplay } from "@/components/generated-prompts-display";
import { PromptEnhancementSection } from "@/components/prompt-enhancement-section";
import { Separator } from "@/components/ui/separator";

interface ContentGridProps {
  formProps: {
    onFormValuesChange: (data: IndonesianPromptFormData) => void;
    onGeneratePrompts: () => void;
    isProcessing: boolean;
  };
  displayProps: {
    indonesianPrompt: string | null;
    onIndonesianPromptChange: (text: string) => void;
    englishPrompt: string | null;
    isProcessingInitial: boolean;
    isTranslatingEdited: boolean;
    enhancementResult: EnhancePromptQualityOutput | null;
    onEnhance: (prompt: string) => void;
    isEnhancing: boolean;
  };
}

function ContentGrid({ formProps, displayProps }: ContentGridProps) {
  return (
    <div className="grid gap-8 md:grid-cols-2">
      <IndonesianPromptForm
        onFormValuesChange={formProps.onFormValuesChange}
        onGeneratePrompts={formProps.onGeneratePrompts}
        isProcessing={formProps.isProcessing}
      />
      <GeneratedPromptsDisplay {...displayProps} />
    </div>
  );
}

// Header component
function Header() {
  return (
    <div className="mb-8">
      <h1 className="text-3xl font-bold">VEO Promptify</h1>
      <p className="text-muted-foreground">Generate and enhance video production prompts</p>
    </div>
  );
}

function formatIndonesianPromptFromData(data: IndonesianPromptFormData): string {
  const parts: string[] = [];
  
  // Format subjects
  if (data.subjects && data.subjects.length > 0) {
    data.subjects.forEach((subject, index) => {
      const subjectParts: string[] = [];
      
      if (subject.name) subjectParts.push(`Nama: ${subject.name}`);
      if (subject.characteristics) subjectParts.push(`Karakteristik: ${subject.characteristics}`);
      if (subject.mainAction) subjectParts.push(`Aksi: ${subject.mainAction}`);
      if (subject.emotion) subjectParts.push(`Ekspresi: ${subject.emotion}`);
      
      if (subjectParts.length > 0) {
        parts.push(`Subjek ${index + 1}: ${subjectParts.join(", ")}`);
      }
    });
  }
  
  // Format scene settings
  if (data.place) parts.push(`Tempat: ${data.place}`);
  if (data.time) parts.push(`Waktu: ${data.time}`);
  if (data.cameraMovement) parts.push(`Pergerakan Kamera: ${data.cameraMovement}`);
  if (data.lighting) parts.push(`Pencahayaan: ${data.lighting}`);
  if (data.videoStyle) parts.push(`Gaya Video: ${data.videoStyle}`);
  if (data.videoMood) parts.push(`Suasana Video: ${data.videoMood}`);
  if (data.soundMusic) parts.push(`Suara/Musik: ${data.soundMusic}`);
  if (data.spokenSentence) parts.push(`Kalimat Diucapkan: ${data.spokenSentence}`);
  if (data.additionalDetails) parts.push(`Detail Tambahan: ${data.additionalDetails}`);
  
  return parts.filter(Boolean).join(", ");
}


export default function VeoPromptifyClientPage() {
  const [currentFormData, setCurrentFormData] = useState<IndonesianPromptFormData>(defaultIndonesianPromptValues);
  const [formDataAtGeneration, setFormDataAtGeneration] = useState<IndonesianPromptFormData | null>(null);
  const [generatedIndonesianPrompt, setGeneratedIndonesianPrompt] = useState<string | null>(null);
  const [finalEnglishPrompt, setFinalEnglishPrompt] = useState<string | null>(null);
  const [enhancementResult, setEnhancementResult] = useState<EnhancePromptQualityOutput | null>(null);
  
  const [isProcessingInitialPrompts, startInitialPromptProcessingTransition] = useTransition();
  const [isTranslatingEdited, startEditedTranslationTransition] = useTransition();
  const [isEnhancing, startEnhancementTransition] = useTransition();

  const { toast } = useToast();

  // Stable callback to update current form data
  const handleFormValuesChange = useCallback((data: IndonesianPromptFormData) => {
    setCurrentFormData(data);
  }, []);

  // Effect to clear prompts if form data changes after generation
  useEffect(() => {
    if (formDataAtGeneration && JSON.stringify(currentFormData) !== JSON.stringify(formDataAtGeneration)) {
      if (generatedIndonesianPrompt !== null || finalEnglishPrompt !== null) {
        setGeneratedIndonesianPrompt(null);
        setFinalEnglishPrompt(null);
        setEnhancementResult(null);
        setFormDataAtGeneration(null); // Reset: prompts are now cleared, new generation will set this again
        // Optionally, inform user why prompts were cleared
        // toast({
        //   title: "Formulir berubah",
        //   description: "Prompt telah dihapus karena input formulir diubah. Silakan buat ulang.",
        //   variant: "default"
        // });
      }
    }
  }, [currentFormData, formDataAtGeneration, generatedIndonesianPrompt, finalEnglishPrompt, toast]);


  const handleGenerateInitialPrompts = useCallback(async () => {
    const hasSubject = currentFormData.subjects && currentFormData.subjects.length > 0;
    const hasAction = hasSubject && currentFormData.subjects.some(s => s.mainAction && s.mainAction.trim() !== "");
    if (!hasSubject || !hasAction) {
      toast({
        title: "Input Kurang",
        description: "Mohon isi setidaknya satu Subjek dan Aksi.",
        variant: "destructive",
      });
      return;
    }

    startInitialPromptProcessingTransition(async () => {
      try {
        const indonesianText = formatIndonesianPromptFromData(currentFormData);
        setGeneratedIndonesianPrompt(indonesianText);

        const formDataWithDefaults = {
          ...currentFormData,
          place: currentFormData.place || '',
          time: currentFormData.time || '',
          cameraMovement: currentFormData.cameraMovement || '',
          lighting: currentFormData.lighting || '',
          videoStyle: currentFormData.videoStyle || '',
          videoMood: currentFormData.videoMood || '',
          soundMusic: currentFormData.soundMusic || '',
          spokenSentence: currentFormData.spokenSentence || '',
          additionalDetails: currentFormData.additionalDetails || '',
          subjects: currentFormData.subjects.map(subject => ({
            name: subject.name || '',
            characteristics: subject.characteristics || '',
            mainAction: subject.mainAction || '',
            emotion: subject.emotion || ''
          }))
        };

        const translationResult = await translateIndonesianPrompt(formDataWithDefaults);
        setFinalEnglishPrompt(translationResult.englishPrompt);
        setEnhancementResult(null); // Clear previous enhancement
        setFormDataAtGeneration(currentFormData); // Store form data used for this generation
      } catch (error) {
        console.error("Initial prompt generation/translation error:", error);
        toast({
          title: "Error Pembuatan Prompt",
          description: "Gagal membuat atau menerjemahkan prompt. Silakan coba lagi.",
          variant: "destructive",
        });
        setGeneratedIndonesianPrompt(null);
        setFinalEnglishPrompt(null);
        setFormDataAtGeneration(null);
      }
    });
  }, [currentFormData, toast]);

  const translateEditedIndonesianText = useCallback(async (editedText: string) => {
    if (!editedText.trim()) {
      setFinalEnglishPrompt(null);
      setEnhancementResult(null);
      return;
    }
    startEditedTranslationTransition(async () => {
      try {
        const result = await translateFreeformIndonesian({ text: editedText });
        setFinalEnglishPrompt(result.translatedText);
        setEnhancementResult(null); // Clear previous enhancement
        // When freeform text is translated, it implies the "form data at generation" is no longer strictly the source.
        // We might want to nullify formDataAtGeneration here, or handle it based on desired UX.
        // For now, let's assume editing the Indonesian prompt means the original form data is less relevant for *this specific generated text pair*.
        // setFormDataAtGeneration(null); // Or update it if we parse editedText back to form fields (complex)
      } catch (error) {
        console.error("Edited translation error:", error);
        toast({
          title: "Error Terjemahan",
          description: "Gagal menerjemahkan prompt yang diedit. Silakan coba lagi.",
          variant: "destructive",
        });
      }
    });
  }, [toast]);
  
  const debouncedTranslateEdited = useCallback(debounce(translateEditedIndonesianText, 750), [translateEditedIndonesianText]);

  const handleIndonesianPromptEdit = useCallback((newText: string) => {
    setGeneratedIndonesianPrompt(newText); 
    debouncedTranslateEdited(newText);
     // If user edits the Indonesian prompt, the link to original structured form data is weakened.
    // Clear formDataAtGeneration so that subsequent direct form edits don't incorrectly clear these manually edited/translated prompts.
    // A new "Generate" click will re-establish this link.
    setFormDataAtGeneration(null);
  }, [debouncedTranslateEdited]);


  const handleEnhancePrompt = useCallback(async (promptToEnhance: string) => {
    if (!promptToEnhance) return;
    startEnhancementTransition(async () => {
      try {
        const result = await enhancePromptQuality({ prompt: promptToEnhance });
        setEnhancementResult(result);
      } catch (error) {
        console.error("Enhancement error:", error);
        toast({
          title: "Enhancement Error",
          description: "Failed to enhance the prompt. Please try again.",
          variant: "destructive",
        });
        setEnhancementResult(null);
      }
    });
  }, [toast]);

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <AppHeader />
      <main className="flex-grow container mx-auto py-8 px-4 md:px-6 animate-fade-in">
        <div className="max-w-7xl mx-auto">
          <Header />
          <ContentGrid 
            formProps={{
              onFormValuesChange: handleFormValuesChange,
              onGeneratePrompts: handleGenerateInitialPrompts,
              isProcessing: isProcessingInitialPrompts
            }}
            displayProps={{
              indonesianPrompt: generatedIndonesianPrompt,
              onIndonesianPromptChange: handleIndonesianPromptEdit,
              englishPrompt: finalEnglishPrompt,
              isProcessingInitial: isProcessingInitialPrompts,
              isTranslatingEdited: isTranslatingEdited,
              enhancementResult: enhancementResult,
              onEnhance: handleEnhancePrompt,
              isEnhancing: isEnhancing
            }}
          />
        </div>
      </main>
      <Footer />
    </div>
  );
}

export function Footer() {
  return (
    <footer className="border-t py-6 md:py-0">
      <div className="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row">
        <div className="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
          <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
            Built by VEO Production Team. &copy; {new Date().getFullYear()}
          </p>
        </div>
      </div>
      <Separator />
    </footer>
  );
}

