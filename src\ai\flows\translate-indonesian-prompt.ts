// This is an autogenerated file from Firebase Studio.

'use server';

/**
 * @fileOverview Automatically translates an Indonesian prompt into English for use with AI video generation tools.
 *
 * - translateIndonesianPrompt - A function that translates the Indonesian prompt.
 * - TranslateIndonesianPromptInput - The input type for the translateIndonesianPrompt function.
 * - TranslateIndonesianPromptOutput - The return type for the translateIndonesianPrompt function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const TranslateIndonesianPromptInputSchema = z.object({
  subjects: z.array(z.object({
    name: z.string().describe('The name of the subject/character in Indonesian.'),
    characteristics: z.string().describe('The characteristics of the subject in Indonesian.'),
    mainAction: z.string().describe('The main action of the subject in Indonesian.'),
    emotion: z.string().describe('The emotion or expression of the subject in Indonesian.'),
  })).describe('Array of subjects/characters in the video.'),
  place: z.string().describe('The location or setting of the video in Indonesian.'),
  time: z.string().describe('The time or duration of the video in Indonesian.'),
  cameraMovement: z.string().describe('The camera movement in the video in Indonesian.'),
  lighting: z.string().describe('The lighting style of the video in Indonesian.'),
  videoStyle: z.string().describe('The overall style of the video in Indonesian.'),
  videoMood: z.string().describe('The mood or atmosphere of the video in Indonesian.'),
  soundMusic: z.string().describe('The sound or music in the video in Indonesian.'),
  spokenSentence: z.string().describe('Any spoken sentence or dialogue in Indonesian.'),
  additionalDetails: z.string().describe('Any additional details for the video in Indonesian.'),
});

const prompt = ai.definePrompt({
  name: 'translateIndonesianPromptPrompt',
  input: {schema: TranslateIndonesianPromptInputSchema},
  output: {schema: TranslateIndonesianPromptInputSchema},  prompt: `Translate the following Indonesian prompt into English. Use the following format:

{{#each subjects}}
Subject:
  Name: {{{this.name}}}
  Characteristics: {{{this.characteristics}}}
  Main Action: {{{this.mainAction}}}
  Expression/Emotion: {{{this.emotion}}}
---
{{/each}}

Place: {{{place}}}
Time: {{{time}}}
Camera Movement: {{{cameraMovement}}}
Lighting: {{{lighting}}}
Video Style: {{{videoStyle}}}
Video Mood: {{{videoMood}}}
Sound/Music: {{{soundMusic}}}
Spoken Sentence: {{{spokenSentence}}}
Additional Details: {{{additionalDetails}}}`
});

export interface TranslateIndonesianPromptOutput {
  englishPrompt: string;
}

export const translateIndonesianPrompt = ai.defineFlow(
  {
    name: 'translateIndonesianPrompt',
    inputSchema: TranslateIndonesianPromptInputSchema,
    outputSchema: z.object({ englishPrompt: z.string() }),
  },  async (input) => {
    const { output } = await prompt(input);
    
    if (!output) {
      throw new Error('Failed to translate prompt');
    }
    
    // Format the output into a readable string
    const parts: string[] = [];

    // Format the subjects
    if (output.subjects && output.subjects.length > 0) {
      output.subjects.forEach((subject, index) => {
        const subjectLines = [];
        subjectLines.push(`Subject ${index + 1}:`);
        if (subject.name) subjectLines.push(`  Name: ${subject.name}`);
        if (subject.characteristics) subjectLines.push(`  Characteristics: ${subject.characteristics}`);
        if (subject.mainAction) subjectLines.push(`  Main Action: ${subject.mainAction}`);
        if (subject.emotion) subjectLines.push(`  Expression/Emotion: ${subject.emotion}`);
        parts.push(subjectLines.join('\n'));
      });
      parts.push(''); // Add empty line between subjects and scene settings
    }

    // Format scene settings
    parts.push('Scene Settings:');
    if (output.place) parts.push(`  Location: ${output.place}`);
    if (output.time) parts.push(`  Time: ${output.time}`);
    if (output.cameraMovement) parts.push(`  Camera Movement: ${output.cameraMovement}`);
    if (output.lighting) parts.push(`  Lighting: ${output.lighting}`);
    if (output.videoStyle) parts.push(`  Video Style: ${output.videoStyle}`);
    if (output.videoMood) parts.push(`  Video Mood: ${output.videoMood}`);
    if (output.soundMusic) parts.push(`  Sound/Music: ${output.soundMusic}`);
    if (output.spokenSentence) parts.push(`  Spoken Sentence: ${output.spokenSentence}`);
    if (output.additionalDetails) parts.push(`  Additional Details: ${output.additionalDetails}`);    
    return { englishPrompt: parts.join('\n') };
  }
);
