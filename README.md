# Firebase Studio

This project is built using the following technologies:

- **React**: A JavaScript library for building user interfaces.
- **Next.js**: A React framework for building server-side rendered and static web applications.
- **TypeScript**: A typed superset of JavaScript that compiles to plain JavaScript.
- **Tailwind CSS**: A utility-first CSS framework for rapidly building custom designs.
- **Firebase**: A platform for building web and mobile applications.

Additional libraries and tools:

- **React Hook Form**: Form validation and handling library
- **Zod**: TypeScript-first schema validation
- **Lucide React**: Modern icon library
- **Shadcn/ui**: Modern UI components built with Radix UI and Tailwind CSS

## Project Features

- Indonesian Prompt Generator with the following features:
  - Subject and Action input
  - Expression and Place description
  - Time setting
  - Camera Movement selection
  - Lighting configuration
  - Video Style options
  - Video Mood settings
  - Sound/Music selection
  - Spoken Sentence input
  - Additional Details
  - Bilingual prompt generation (Indonesian & English)

## Project Structure

The project follows a component-based architecture with the following structure:

- `src/components/`: React components
  - `ui/`: Reusable UI components
  - `*.tsx`: Feature-specific components
- `src/lib/`: Utility functions and schemas
- `src/app/`: Next.js application routes

To get started, take a look at src/app/page.tsx.
