import { z } from "zod";

// Define a schema for a single subject
const SubjectSchema = z.object({
  name: z.string().min(1, "Subject name is required"),
  characteristics: z.string().optional(),
  mainAction: z.string().min(1, "Main action is required"),
  emotion: z.string().optional(),
});

export const IndonesianPromptSchema = z.object({
  subjects: z.array(SubjectSchema).min(1, "At least one subject is required"),
  place: z.string().optional(),
  time: z.string().optional(),
  dialogType: z.string().optional(),
  cameraMovement: z.string().optional(),
  lighting: z.string().optional(),
  videoStyle: z.string().optional(),
  videoMood: z.string().optional(),
  soundMusic: z.string().optional(),
  spokenSentence: z.string().optional(),
  additionalDetails: z.string().optional(),
});

export type SubjectData = z.infer<typeof SubjectSchema>;
export type IndonesianPromptFormData = z.infer<typeof IndonesianPromptSchema>;

export const defaultIndonesianPromptValues: IndonesianPromptFormData = {
  subjects: [{ name: "", characteristics: "", mainAction: "", emotion: "" }],
  place: "",
  time: "",
  dialogType: "",
  cameraMovement: "",
  lighting: "",
  videoStyle: "",
  videoMood: "",
  soundMusic: "",
  spokenSentence: "",
  additionalDetails: "",
};
