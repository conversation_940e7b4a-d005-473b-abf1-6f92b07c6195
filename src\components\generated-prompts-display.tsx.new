"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { ArrowDownToLine, Languages, Pencil, Info, Star, Sparkles } from "lucide-react";
import { EnhancePromptQualityOutput } from "@/ai/flows/enhance-prompt-quality";

interface GeneratedPromptsDisplayProps {
  indonesianPrompt: string | null;
  onIndonesianPromptChange: (text: string) => void;
  englishPrompt: string | null;
  isProcessingInitial: boolean;
  isTranslatingEdited: boolean;
  enhancementResult: EnhancePromptQualityOutput | null;
  onEnhance: (prompt: string) => void;
  isEnhancing: boolean;
}

export function GeneratedPromptsDisplay({ 
  indonesianPrompt, 
  onIndonesianPromptChange, 
  englishPrompt,
  isProcessingInitial,
  isTranslatingEdited,
  enhancementResult,
  onEnhance,
  isEnhancing
}: GeneratedPromptsDisplayProps) {
  const [localIndonesianPrompt, setLocalIndonesianPrompt] = useState(indonesianPrompt || "");
  const [keyIndonesian, setKeyIndonesian] = useState(0);
  const [keyEnglish, setKeyEnglish] = useState(0);

  useEffect(() => {
    setLocalIndonesianPrompt(indonesianPrompt || "");
    if (indonesianPrompt !== null) {
      setKeyIndonesian(prev => prev + 1);
    }
  }, [indonesianPrompt]);

  useEffect(() => {
    if (englishPrompt !== null) {
      setKeyEnglish(prev => prev + 1);
    }
  }, [englishPrompt]);

  return (
    <div className="space-y-6">
      {/* Indonesian Prompt Card */}
      <Card className="border-primary/20 overflow-hidden">
        <CardHeader className="bg-primary/5 pb-4">
          <CardTitle className="font-headline text-xl flex items-center gap-2">
            <Pencil className="h-5 w-5 text-primary" />
            Prompt Bahasa Indonesia (Dapat Diedit)
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-4">
          {isProcessingInitial ? (
            <div className="space-y-2 py-2">
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-11/12" />
              <Skeleton className="h-6 w-4/5" />
            </div>
          ) : indonesianPrompt !== null ? (
            <div key={keyIndonesian} className="animate-fade-in">
              <Textarea
                value={localIndonesianPrompt}
                onChange={(e) => {
                  setLocalIndonesianPrompt(e.target.value);
                  onIndonesianPromptChange(e.target.value);
                }}
                placeholder="Prompt Bahasa Indonesia akan muncul di sini setelah dibuat dari formulir..."
                className="h-32 bg-card border-input focus:border-primary focus-visible:ring-primary/20 transition-all duration-200"
              />
            </div>
          ) : (
            <div className="text-muted-foreground h-32 flex flex-col items-center justify-center gap-2 border border-dashed rounded-md p-4 bg-muted/10">
              <ArrowDownToLine className="h-6 w-6 text-muted-foreground/60" />
              <p className="text-center">Isi formulir dan klik "Buat Prompt (ID & EN)" untuk melihat prompt Bahasa Indonesia di sini.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* English Prompt Card */}
      <Card className="border-blue-500/20 overflow-hidden">
        <CardHeader className="bg-blue-500/5 pb-4">
          <CardTitle className="font-headline text-xl flex items-center gap-2">
            <Languages className="h-5 w-5 text-blue-500" />
            Final Prompt (English - Read-only)
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-4">
          {isProcessingInitial || isTranslatingEdited ? (
            <div className="space-y-2 py-2">
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-11/12" />
              <Skeleton className="h-6 w-4/5" />
            </div>
          ) : englishPrompt ? (
            <div key={keyEnglish} className="animate-fade-in">
              <Textarea
                value={englishPrompt}
                readOnly
                className="h-32 bg-muted/20 border-dashed focus-visible:ring-blue-500/20"
              />
            </div>
          ) : (
            <div className="text-muted-foreground h-32 flex flex-col items-center justify-center gap-2 border border-dashed rounded-md p-4 bg-muted/10">
              <Languages className="h-6 w-6 text-muted-foreground/60" />
              <p className="text-center">Prompt Bahasa Inggris akan muncul di sini setelah diterjemahkan.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Enhancement Card */}
      {englishPrompt && (
        <Card className="border-yellow-500/20 overflow-hidden">
          <CardHeader className="bg-yellow-500/5 pb-4">
            <CardTitle className="font-headline text-xl flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-500" />
              VEO Enhancement
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            {isEnhancing ? (
              <div className="space-y-2 py-2">
                <Skeleton className="h-6 w-full" />
                <Skeleton className="h-6 w-11/12" />
                <Skeleton className="h-6 w-4/5" />
              </div>
            ) : enhancementResult ? (
              <div className="space-y-4 animate-fade-in">
                <div className="p-4 rounded-md bg-yellow-500/5 border border-yellow-500/20">
                  <h3 className="font-medium text-sm flex items-center gap-1 mb-2 text-muted-foreground">
                    <Info className="h-4 w-4" />
                    Analisis dan Perbaikan
                  </h3>
                  <div className="text-sm leading-relaxed text-foreground/80">
                    {enhancementResult.explanation}
                  </div>
                </div>
                <div>
                  <h3 className="font-medium text-sm flex items-center gap-1 mb-2 text-muted-foreground">
                    <Sparkles className="h-4 w-4 text-yellow-500" />
                    Enhanced Prompt (VEO Standard)
                  </h3>
                  <Textarea
                    value={enhancementResult.enhancedPrompt}
                    readOnly
                    className="min-h-[120px] bg-card focus-visible:ring-yellow-500/20 border-yellow-500/20"
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Klik tombol di bawah untuk meningkatkan kualitas prompt berdasarkan standar VEO.
                </p>
                <Button
                  onClick={() => englishPrompt && onEnhance(englishPrompt)}
                  className="w-full bg-yellow-500 hover:bg-yellow-600 text-white gap-1"
                >
                  <Sparkles className="h-4 w-4" />
                  Enhance Prompt
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
