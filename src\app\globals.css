@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 220 17% 95%; /* Light Gray #F0F2F5 */
    --foreground: 220 10% 25%; /* Dark Gray for text */
    --card: 0 0% 100%; /* White */
    --card-foreground: 220 10% 25%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 10% 25%;
    --primary: 228 62% 59%; /* Muted Indigo #556CD6 */
    --primary-foreground: 0 0% 100%; /* White */
    --secondary: 220 15% 90%; /* Slightly darker gray for secondary elements */
    --secondary-foreground: 220 10% 25%;
    --muted: 220 15% 85%; /* Muted gray */
    --muted-foreground: 220 10% 45%;
    --accent: 228 40% 70%; /* Soft Lavender #8A9BD6 */
    --accent-foreground: 0 0% 100%; /* White */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 220 10% 88%; /* Mid Gray */
    --input: 220 10% 88%;
    --ring: 228 62% 59%; /* Muted Indigo for focus rings */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Define dark mode colors if needed, for now, let's assume light mode only based on proposal */
    --background: 220 10% 10%;
    --foreground: 220 17% 95%;
    --card: 220 10% 15%;
    --card-foreground: 220 17% 95%;
    --popover: 220 10% 15%;
    --popover-foreground: 220 17% 95%;
    --primary: 228 62% 59%;
    --primary-foreground: 0 0% 100%;
    --secondary: 220 10% 20%;
    --secondary-foreground: 220 17% 95%;
    --muted: 220 10% 25%;
    --muted-foreground: 220 17% 70%;
    --accent: 228 40% 70%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 220 10% 30%;
    --input: 220 10% 30%;
    --ring: 228 62% 59%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .fade-in {
    animation: fadeInAnimation 0.5s ease-in-out;
  }
}

@keyframes fadeInAnimation {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
